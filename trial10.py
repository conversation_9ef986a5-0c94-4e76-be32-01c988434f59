def diagonalDifference(n,arr):
    # Write your code here
    count=0
    count1=0
    for i in range(n):
        for j in range(n):
            if i==j:
                print(i,j)
                count+=arr[i][j]
            if (i+j)==2:
                print(i,j)
                count1+=arr[i][j]
    print(count,count1)
    if (count>count1):
        d=count-count1
    else:
        d=count1-count
    return d
                    

if __name__ == '__main__':
    n = int(input().strip())
    arr = []
    for _ in range(n):
        arr.append(list(map(int, input().rstrip().split())))

    result = diagonalDifference(n,arr)

    print(result)
