r1=int(input('enter the first row: '))
c1=int(input('enter the first colum: '))
r2=int(input('enter the second row: '))
c2=int(input('enter the second colum: '))
mat1=[[int(input()) for i in range(c1)]for j in range(r1)]
mat2=[[int(input()) for i in range(c2)]for j in range(r2)]
if r1==r2 and c1==c2:
    ls1=[[(mat1[i][j]+mat2[i][j]) for j in range(c1)]for i in range(r1)]
    print(ls1)
else:
    print('matrix not add')