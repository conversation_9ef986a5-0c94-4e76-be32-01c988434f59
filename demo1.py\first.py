# # question 8
# f=lambda x,y:[a[i] for i in range(len(a)) if a[i] in b]


# a=[1,2,3,4,5,6,8,9,10]
# b=[1,3,5,22,7,8]
# x=f(a,b)
# print(x)

#        #question 7

# # keys=['ten', 'twenty', 'thirty']
# # values=[10,20,30]
# # d={}
# # for i in range(len(values)):
# #     d.update({keys[i]:values[i]})
# # print(d)

cube = lambda x:x**3
    
def fibonacci(n):
    ls=[]
    a=0
    ls.append(a)
    if n==1:
        return ls
    b=1
    ls.append(b)
    if n==2:
        return ls
    for i in range(n-2):
        c=a+b
        ls.append(c)
        a,b=b,c
        
    return ls
if __name__ == '__main__':
    n = int(input())
    print(list(map(cube, fibonacci(n))))