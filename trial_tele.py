import telegram
from telegram.ext import <PERSON><PERSON><PERSON>, Message<PERSON><PERSON><PERSON>, Filters

# Your bot token and chat IDs
TOKEN = '6295207819:AAGYQN7EHyxI5xBGXmxuAumTAN7_iWGBKQk'
MY_CHAT_ID = '920040409'
GROUP_CHAT_ID = '1784540972'

# Create the bot instance and set the full access
bot = telegram.Bot(token=TOKEN)
bot.send_chat_action(chat_id=MY_CHAT_ID, action=telegram.ChatAction.TYPING)

# Define a function to forward the file from the group to the specified chat
def forward_file(bot, update):
    message = update.message
    file = message.document
    # Check if the message is from the group chat
    if message.chat_id == GROUP_CHAT_ID:
        # Forward the file to the specified chat
        bot.forward_message(chat_id=MY_CHAT_ID, from_chat_id=GROUP_CHAT_ID, message_id=message.message_id)
        bot.send_message(chat_id=MY_CHAT_ID, text="A new file has been forwarded!")
        
# Create an updater and add the message handler
updater = Updater(TOKEN)
dispatcher = updater.dispatcher
dispatcher.add_handler(MessageHandler(Filters.document, forward_file))

# Start the bot
updater.start_polling()
