'''f=open("trial4.txt","w")
str=input("enter value")
f.write(str)
f.close()'''

'''f=open("trial4.txt","a")
str=input("enter value")
f.write(str)
f.close()'''

# find file pointer postion function -tell()
'''f=open("trial4.txt","a")
str=input("enter value")
y=f.tell()
print(y)
f.write(str)
f.close()'''

'''f=open("trial4.txt","r")
y=f.tell()
print(y)
x=f.read()
print(x)
f.close()'''

'''f=open("trial4.txt","w")
#y=f.tell()
#print(y)
str=input()
x=f.write(str)
y=f.tell()
print(y)
f.close()
'''

f=open("trial4.txt","r+")
y=f.tell()
print(y)
#x=f.read()
#print(x)
str=input()
f.write(str)
z=f.read()
print(z)
f.close()
