#code for cropping

import cv2
import os

# Set the path to the folder containing the images
folder_path = r'C:\Users\<USER>\OneDrive\Desktop\new'

# Set the path to the folder where you want to save the cropped images
save_path = r'C:\Users\<USER>\OneDrive\Desktop\new2'

# Set the size and position of the area you want to crop
x = 42
y = 436
w = 543
h = 864

seq_num=1
# Loop through all the images in the folder
for filename in os.listdir(folder_path):
    if filename.endswith('.jpg'): # change file extension if necessary
        # Read the image
        img_path = os.path.join(folder_path, filename)
        img = cv2.imread(img_path)

        # Crop the image
        crop_img = img[y:y+h, x:x+w]

        # Save the cropped image with a new namea
        save_name = f'image{seq_num:04d}.jpg'
        save_path_file = os.path.join(save_path, save_name)
        cv2.imwrite(save_path_file, crop_img)
        seq_num += 1