Python 3.11.3 (tags/v3.11.3:f3909b8, Apr  4 2023, 23:49:59) [MSC v.1934 64 bit (AMD64)] on win32
Type "help", "copyright", "credits" or "license()" for more information.
bin(2)
'0b10'
bin(-5)
'-0b101'
>>> bin(g)
Traceback (most recent call last):
  File "<pyshell#2>", line 1, in <module>
    bin(g)
NameError: name 'g' is not defined
>>> oct(1)
'0o1'
>>> oct(9)
'0o11'
>>> oct(40)
'0o50'
>>> hex(1)
'0x1'
>>> hex(8)
'0x8'
>>> hex(100)
'0x64'
>>> n=int(input())
a=n
Traceback (most recent call last):
  File "<pyshell#9>", line 1, in <module>
    n=int(input())
ValueError: invalid literal for int() with base 10: 'a=n'
