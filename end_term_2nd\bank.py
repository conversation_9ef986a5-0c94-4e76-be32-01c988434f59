count=0
while True:
    n=int(input('press1-D\npress2-W\npress3-total amount'))
    if n==1:
        d=int(input('Deposite amount:'))
        count+=d
    elif n==2:
        w=int(input('withdraw amount:'))
        if w<=count:
            count-=w
        else:
            print('inserfient amount:',count)
            break
    elif n==3:
        print("total amount:",count)
        break
    else:
        ('press wrong number')
        break