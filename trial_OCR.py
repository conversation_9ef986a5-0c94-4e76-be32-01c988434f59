import pytesseract
from PIL import Image
import os

# Set the path to the folder containing the images
folder_path = 'C:/Users/<USER>/OneDrive/Desktop/trial1'

# Open a new text file to save the extracted text
with open('C:/Users/<USER>/OneDrive/Desktop/extracted_text1.txt', 'w', encoding='utf-8') as f:

    # Loop through all the images in the folder
    for filename in os.listdir(folder_path):
        if filename.endswith('.jpg'): # change file extension if necessary
            # Read the image
            img_path = os.path.join(folder_path, filename)
            img = Image.open(img_path)

            # Extract text from image
            text = pytesseract.image_to_string(img, lang='hin')

            # Write the extracted text into the file
            f.write(text)
            f.write('\n') # add a newline character for each image's text to be separated in the file

# Close the file
f.close()
