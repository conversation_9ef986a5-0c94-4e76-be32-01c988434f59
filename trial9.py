if __name__ == '__main__':
    ls=[]
    ls1=[]
    ls2=[]
    ls3=[]
    for _ in range(int(input())):
        name = input()
        score = float(input())
        ls3.append(score)
        ls.append([name,score])
    ls1=list(set(ls3))
    ls1.sort()
    for i in range(len(ls1)):
        if ls1[1]==ls[i][1]:
            ls2.append(ls[i][0])
    ls2.sort()
    for i in ls2:
        print(i)


if __name__ == '__main__':
    l=[]
    s=[]
    for _ in range(int(input())):
        name = input()
        score = float(input())
        a=[score,name]
        l.append(a)
        s.append(score)
    l.sort()
    s.sort()
    b=s.count(l[0][0])
    for i in range(1,b+1):
        l.pop(0)
    min=l[0][0]
    for i in l:
        if(min==i[0]):
            print(i[1])
