import math
class Solution:
    def checkIfPossible(self, n : int) -> List[int]:
        # code here
        l=[-1]
        flag=0
        for i in range(1,int(math.pow(10,9))+1):
            for j in range(1,int(math.pow(10,9))+1):
                if math.log(i,2)+math.log(j,3)==n:
                    l.clear()
                    l.append(i)
                    l.append(j)
                    flag=1
                    break
            if flag==1:
                break
        return l
