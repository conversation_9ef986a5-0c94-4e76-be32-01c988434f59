import os
from PIL import Image

# Define the range of intensities for each channel
r_range = range(135, 250)  # Red intensity range: 100-150
g_range = range(135, 250)   # Green intensity range: 50-100
b_range = range(135, 250)     # Blue intensity range: 0-50

# Get a list of all files in the directory
directory = r"C:\Users\<USER>\OneDrive\Desktop\new2"
files = os.listdir(directory)

# Loop over all the image files in the directory
for file in files:
    if file.endswith(".png") or file.endswith(".jpg"):
        # Open the image file
        img = Image.open(os.path.join(directory, file))

        # Loop over all the pixels in the image
        for x in range(img.width):
            for y in range(img.height):
                pixel_color = img.getpixel((x, y))
                r, g, b = pixel_color[0], pixel_color[1], pixel_color[2]
                if r in r_range and g in g_range and b in b_range:
                    img.putpixel((x, y), (255, 255, 255)) # set the pixel to white

        # Save the modified image with the same name in a new directory
        output_directory = r"C:\Users\<USER>\OneDrive\Desktop\new3"
        img.save(os.path.join(output_directory, file))